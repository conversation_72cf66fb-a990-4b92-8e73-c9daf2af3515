#!/bin/bash

# 视频优化一键部署脚本
# 适用于love.yuh.cool域名

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${BLUE}🚀 Love Website 视频优化部署脚本${NC}"
echo -e "${BLUE}====================================${NC}"

# 检查当前目录
if [ ! -f "config.js" ]; then
    echo -e "${RED}❌ 请在love项目根目录运行此脚本${NC}"
    exit 1
fi

# 第一步：运行视频优化
echo -e "${YELLOW}📹 第一步：优化视频文件...${NC}"
if [ -f "scripts/video-optimizer-2k.sh" ]; then
    chmod +x scripts/video-optimizer-2k.sh
    ./scripts/video-optimizer-2k.sh
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✅ 视频优化完成${NC}"
    else
        echo -e "${RED}❌ 视频优化失败${NC}"
        exit 1
    fi
else
    echo -e "${RED}❌ 未找到视频优化脚本${NC}"
    exit 1
fi

# 第二步：更新HTML文件
echo -e "${YELLOW}🔄 第二步：更新HTML文件引用...${NC}"
if [ -d "html" ]; then
    # 备份原始文件
    mkdir -p backup/html
    cp html/*.html backup/html/ 2>/dev/null
    
    # 更新script引用
    for file in html/*.html; do
        if [ -f "$file" ]; then
            sed -i.bak 's|video-manager\.js|enhanced-video-manager.js|g' "$file"
            echo -e "   ✅ 已更新: $(basename "$file")"
        fi
    done
    
    echo -e "${GREEN}✅ HTML文件更新完成${NC}"
else
    echo -e "${YELLOW}⚠️ 未找到html目录，跳过HTML更新${NC}"
fi

# 第三步：检查优化结果
echo -e "${YELLOW}📊 第三步：检查优化结果...${NC}"
if [ -d "background/optimized" ]; then
    echo -e "${BLUE}优化后的视频文件：${NC}"
    find background/optimized -name "*.mp4" -o -name "*.webm" | while read -r file; do
        size=$(du -h "$file" | cut -f1)
        echo -e "   📁 $(basename "$file"): $size"
    done
    
    # 计算总体积减少
    original_size=$(du -sh background --exclude=optimized 2>/dev/null | cut -f1)
    optimized_size=$(du -sh background/optimized 2>/dev/null | cut -f1)
    
    echo -e "${BLUE}📈 优化统计：${NC}"
    echo -e "   原始总大小: $original_size"
    echo -e "   优化后大小: $optimized_size"
else
    echo -e "${RED}❌ 未找到优化后的视频文件${NC}"
fi

# 第四步：生成测试页面
echo -e "${YELLOW}🧪 第四步：生成测试页面...${NC}"
cat > test-optimization.html << 'EOF'
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>视频优化测试 - love.yuh.cool</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 10px; }
        .test-item { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .video-container { width: 100%; height: 200px; background: #000; margin: 10px 0; }
        .video-container video { width: 100%; height: 100%; object-fit: cover; }
        .btn { padding: 10px 20px; margin: 5px; border: none; border-radius: 5px; cursor: pointer; }
        .btn-primary { background: #007bff; color: white; }
        .metrics { display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 10px; margin: 10px 0; }
        .metric { background: #f8f9fa; padding: 10px; text-align: center; border-radius: 5px; }
        .metric-value { font-size: 20px; font-weight: bold; color: #007bff; }
        .log { background: #f8f9fa; padding: 10px; height: 150px; overflow-y: auto; font-family: monospace; font-size: 12px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎬 视频优化测试 - love.yuh.cool</h1>
        
        <div class="test-item">
            <h3>📊 系统信息</h3>
            <div class="metrics">
                <div class="metric">
                    <div class="metric-value" id="networkType">检测中...</div>
                    <div>网络类型</div>
                </div>
                <div class="metric">
                    <div class="metric-value" id="networkSpeed">检测中...</div>
                    <div>网络速度</div>
                </div>
            </div>
        </div>

        <div class="test-item">
            <h3>🎥 视频加载测试</h3>
            <button class="btn btn-primary" onclick="testOriginal()">测试原始视频</button>
            <button class="btn btn-primary" onclick="testOptimized()">测试优化视频</button>
            
            <div class="video-container">
                <video id="testVideo" muted loop controls></video>
            </div>
            
            <div class="metrics">
                <div class="metric">
                    <div class="metric-value" id="loadTime">0</div>
                    <div>加载时间(秒)</div>
                </div>
                <div class="metric">
                    <div class="metric-value" id="videoFormat">未知</div>
                    <div>视频格式</div>
                </div>
            </div>
        </div>

        <div class="test-item">
            <h3>📝 测试日志</h3>
            <div class="log" id="testLog"></div>
        </div>
    </div>

    <script src="/config.js"></script>
    <script src="/enhanced-video-manager.js"></script>
    <script>
        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            detectSystemInfo();
            log('🚀 测试页面已加载');
        });

        function detectSystemInfo() {
            if (navigator.connection) {
                document.getElementById('networkType').textContent = navigator.connection.effectiveType || '未知';
                document.getElementById('networkSpeed').textContent = (navigator.connection.downlink || 0) + ' Mbps';
            }
        }

        async function testOriginal() {
            log('🎬 开始测试原始视频...');
            const startTime = performance.now();
            
            try {
                const video = document.getElementById('testVideo');
                video.src = 'https://love.yuh.cool/background/flower-bg.mp4';
                
                await new Promise((resolve, reject) => {
                    video.addEventListener('canplaythrough', resolve, { once: true });
                    video.addEventListener('error', reject, { once: true });
                    video.load();
                });
                
                const loadTime = (performance.now() - startTime) / 1000;
                document.getElementById('loadTime').textContent = loadTime.toFixed(2);
                document.getElementById('videoFormat').textContent = '原始MP4';
                
                log(`✅ 原始视频加载完成，耗时: ${loadTime.toFixed(2)}s`);
                
            } catch (error) {
                log(`❌ 原始视频加载失败: ${error.message}`);
            }
        }

        async function testOptimized() {
            log('🎬 开始测试优化视频...');
            const startTime = performance.now();
            
            try {
                const video = document.getElementById('testVideo');
                video.src = 'https://love.yuh.cool/background/optimized/flower-bg_h265_optimized.mp4';
                
                await new Promise((resolve, reject) => {
                    video.addEventListener('canplaythrough', resolve, { once: true });
                    video.addEventListener('error', reject, { once: true });
                    video.load();
                });
                
                const loadTime = (performance.now() - startTime) / 1000;
                document.getElementById('loadTime').textContent = loadTime.toFixed(2);
                document.getElementById('videoFormat').textContent = 'H.265优化';
                
                log(`✅ 优化视频加载完成，耗时: ${loadTime.toFixed(2)}s`);
                
            } catch (error) {
                log(`❌ 优化视频加载失败，尝试H.264版本...`);
                
                try {
                    video.src = 'https://love.yuh.cool/background/optimized/flower-bg_h264_optimized.mp4';
                    await new Promise((resolve, reject) => {
                        video.addEventListener('canplaythrough', resolve, { once: true });
                        video.addEventListener('error', reject, { once: true });
                        video.load();
                    });
                    
                    const loadTime = (performance.now() - startTime) / 1000;
                    document.getElementById('loadTime').textContent = loadTime.toFixed(2);
                    document.getElementById('videoFormat').textContent = 'H.264优化';
                    
                    log(`✅ H.264优化视频加载完成，耗时: ${loadTime.toFixed(2)}s`);
                    
                } catch (error2) {
                    log(`❌ 优化视频加载失败: ${error2.message}`);
                }
            }
        }

        function log(message) {
            const logElement = document.getElementById('testLog');
            const timestamp = new Date().toLocaleTimeString();
            logElement.innerHTML += `[${timestamp}] ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
        }
    </script>
</body>
</html>
EOF

echo -e "${GREEN}✅ 测试页面已生成: test-optimization.html${NC}"

# 第五步：生成部署报告
echo -e "${YELLOW}📋 第五步：生成部署报告...${NC}"
{
    echo "# 视频优化部署报告"
    echo "部署时间: $(date)"
    echo "域名: love.yuh.cool"
    echo ""
    echo "## 优化结果"
    if [ -d "background/optimized" ]; then
        find background/optimized -name "*.mp4" -o -name "*.webm" | while read -r file; do
            size=$(du -h "$file" | cut -f1)
            echo "- $(basename "$file"): $size"
        done
    fi
    echo ""
    echo "## 测试链接"
    echo "- 测试页面: https://love.yuh.cool/test-optimization.html"
    echo "- 主页面: https://love.yuh.cool/"
    echo ""
    echo "## 下一步"
    echo "1. 访问测试页面验证优化效果"
    echo "2. 检查各页面的视频加载速度"
    echo "3. 监控服务器带宽使用情况"
    echo "4. 考虑配置CDN进一步优化"
} > deployment-report.md

echo -e "${GREEN}✅ 部署报告已生成: deployment-report.md${NC}"

# 完成
echo -e "${GREEN}🎉 视频优化部署完成！${NC}"
echo -e "${BLUE}📋 部署总结：${NC}"
echo -e "   ✅ 视频文件已优化"
echo -e "   ✅ HTML文件已更新"
echo -e "   ✅ 测试页面已生成"
echo -e "   ✅ 部署报告已生成"
echo ""
echo -e "${BLUE}🔗 测试链接：${NC}"
echo -e "   🌐 https://love.yuh.cool/test-optimization.html"
echo ""
echo -e "${BLUE}💡 建议：${NC}"
echo -e "   1. 立即访问测试页面验证效果"
echo -e "   2. 检查各页面的加载速度改善"
echo -e "   3. 监控用户反馈和性能指标"

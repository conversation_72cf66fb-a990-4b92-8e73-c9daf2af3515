<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>视频优化测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        
        .video-container {
            position: relative;
            width: 100%;
            height: 300px;
            background: #000;
            border-radius: 5px;
            overflow: hidden;
            margin: 10px 0;
        }
        
        .video-container video {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        
        .metrics {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin: 10px 0;
        }
        
        .metric-card {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            text-align: center;
        }
        
        .metric-value {
            font-size: 24px;
            font-weight: bold;
            color: #007bff;
        }
        
        .metric-label {
            font-size: 12px;
            color: #666;
            margin-top: 5px;
        }
        
        .status {
            padding: 5px 10px;
            border-radius: 3px;
            font-size: 12px;
            font-weight: bold;
        }
        
        .status.loading { background: #fff3cd; color: #856404; }
        .status.success { background: #d4edda; color: #155724; }
        .status.error { background: #f8d7da; color: #721c24; }
        
        .controls {
            margin: 10px 0;
        }
        
        .btn {
            padding: 8px 16px;
            margin: 5px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        
        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-warning { background: #ffc107; color: black; }
        
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎬 视频优化测试页面</h1>
        
        <!-- 网络和设备信息 -->
        <div class="test-section">
            <h3>📊 系统信息</h3>
            <div class="metrics">
                <div class="metric-card">
                    <div class="metric-value" id="networkType">检测中...</div>
                    <div class="metric-label">网络类型</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value" id="networkSpeed">检测中...</div>
                    <div class="metric-label">网络速度</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value" id="deviceMemory">检测中...</div>
                    <div class="metric-label">设备内存</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value" id="deviceCores">检测中...</div>
                    <div class="metric-label">CPU核心</div>
                </div>
            </div>
        </div>

        <!-- 视频加载测试 -->
        <div class="test-section">
            <h3>🎥 视频加载测试</h3>
            <div class="controls">
                <button class="btn btn-primary" onclick="testOriginalVideo()">测试原始视频</button>
                <button class="btn btn-success" onclick="testCompressedVideo()">测试压缩视频</button>
                <button class="btn btn-warning" onclick="testProgressiveLoading()">测试渐进式加载</button>
            </div>
            
            <div class="video-container">
                <video id="testVideo" muted loop></video>
                <div id="videoStatus" class="status loading">准备就绪</div>
            </div>
            
            <div class="metrics">
                <div class="metric-card">
                    <div class="metric-value" id="loadTime">0</div>
                    <div class="metric-label">加载时间 (秒)</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value" id="fileSize">0</div>
                    <div class="metric-label">文件大小 (MB)</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value" id="quality">未知</div>
                    <div class="metric-label">视频质量</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value" id="format">未知</div>
                    <div class="metric-label">视频格式</div>
                </div>
            </div>
        </div>

        <!-- 性能对比 -->
        <div class="test-section">
            <h3>📈 性能对比</h3>
            <table style="width: 100%; border-collapse: collapse;">
                <thead>
                    <tr style="background: #f8f9fa;">
                        <th style="padding: 10px; border: 1px solid #ddd;">测试项目</th>
                        <th style="padding: 10px; border: 1px solid #ddd;">原始视频</th>
                        <th style="padding: 10px; border: 1px solid #ddd;">压缩视频</th>
                        <th style="padding: 10px; border: 1px solid #ddd;">改善程度</th>
                    </tr>
                </thead>
                <tbody id="comparisonTable">
                    <tr>
                        <td style="padding: 10px; border: 1px solid #ddd;">加载时间</td>
                        <td style="padding: 10px; border: 1px solid #ddd;" id="originalTime">-</td>
                        <td style="padding: 10px; border: 1px solid #ddd;" id="compressedTime">-</td>
                        <td style="padding: 10px; border: 1px solid #ddd;" id="timeImprovement">-</td>
                    </tr>
                    <tr>
                        <td style="padding: 10px; border: 1px solid #ddd;">文件大小</td>
                        <td style="padding: 10px; border: 1px solid #ddd;" id="originalSize">-</td>
                        <td style="padding: 10px; border: 1px solid #ddd;" id="compressedSize">-</td>
                        <td style="padding: 10px; border: 1px solid #ddd;" id="sizeImprovement">-</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <!-- 日志输出 -->
        <div class="test-section">
            <h3>📝 测试日志</h3>
            <div class="log" id="testLog"></div>
            <button class="btn btn-warning" onclick="clearLog()">清空日志</button>
        </div>
    </div>

    <!-- 引入配置和管理器 -->
    <script src="/config.js"></script>
    <script src="/enhanced-video-manager.js"></script>
    
    <script>
        let testResults = {
            original: {},
            compressed: {}
        };

        // 初始化页面
        document.addEventListener('DOMContentLoaded', function() {
            detectSystemInfo();
            log('🚀 视频优化测试页面已加载');
        });

        // 检测系统信息
        function detectSystemInfo() {
            // 网络信息
            if (navigator.connection) {
                document.getElementById('networkType').textContent = navigator.connection.effectiveType || '未知';
                document.getElementById('networkSpeed').textContent = (navigator.connection.downlink || 0) + ' Mbps';
            }

            // 设备信息
            document.getElementById('deviceMemory').textContent = (navigator.deviceMemory || 'N/A') + ' GB';
            document.getElementById('deviceCores').textContent = navigator.hardwareConcurrency || 'N/A';
        }

        // 测试原始视频
        async function testOriginalVideo() {
            log('🎬 开始测试原始视频...');
            const startTime = performance.now();
            
            try {
                const video = document.getElementById('testVideo');
                const status = document.getElementById('videoStatus');
                
                status.textContent = '加载中...';
                status.className = 'status loading';
                
                // 使用花朵背景作为测试
                const videoUrl = '/background/flower-bg.mp4';
                await loadVideo(video, videoUrl);
                
                const loadTime = (performance.now() - startTime) / 1000;
                
                // 更新UI
                document.getElementById('loadTime').textContent = loadTime.toFixed(2);
                document.getElementById('quality').textContent = '原始';
                document.getElementById('format').textContent = 'MP4';
                
                status.textContent = '加载成功';
                status.className = 'status success';
                
                // 保存结果
                testResults.original = {
                    loadTime: loadTime,
                    size: 63, // 已知大小
                    quality: 'original'
                };
                
                document.getElementById('originalTime').textContent = loadTime.toFixed(2) + 's';
                document.getElementById('originalSize').textContent = '63 MB';
                
                log(`✅ 原始视频加载完成，耗时: ${loadTime.toFixed(2)}s`);
                
            } catch (error) {
                log(`❌ 原始视频加载失败: ${error.message}`);
                document.getElementById('videoStatus').textContent = '加载失败';
                document.getElementById('videoStatus').className = 'status error';
            }
        }

        // 测试压缩视频
        async function testCompressedVideo() {
            log('🎬 开始测试压缩视频...');
            const startTime = performance.now();
            
            try {
                const video = document.getElementById('testVideo');
                const status = document.getElementById('videoStatus');
                
                status.textContent = '加载中...';
                status.className = 'status loading';
                
                // 尝试加载压缩版本
                const videoUrl = '/background/compressed/flower-bg_720p.mp4';
                await loadVideo(video, videoUrl);
                
                const loadTime = (performance.now() - startTime) / 1000;
                
                // 更新UI
                document.getElementById('loadTime').textContent = loadTime.toFixed(2);
                document.getElementById('quality').textContent = '720p';
                document.getElementById('format').textContent = 'MP4';
                
                status.textContent = '加载成功';
                status.className = 'status success';
                
                // 保存结果
                testResults.compressed = {
                    loadTime: loadTime,
                    size: 15, // 估算大小
                    quality: '720p'
                };
                
                document.getElementById('compressedTime').textContent = loadTime.toFixed(2) + 's';
                document.getElementById('compressedSize').textContent = '~15 MB';
                
                // 计算改善程度
                if (testResults.original.loadTime) {
                    const timeImprovement = ((testResults.original.loadTime - loadTime) / testResults.original.loadTime * 100).toFixed(1);
                    const sizeImprovement = ((63 - 15) / 63 * 100).toFixed(1);
                    
                    document.getElementById('timeImprovement').textContent = `${timeImprovement}% 更快`;
                    document.getElementById('sizeImprovement').textContent = `${sizeImprovement}% 更小`;
                }
                
                log(`✅ 压缩视频加载完成，耗时: ${loadTime.toFixed(2)}s`);
                
            } catch (error) {
                log(`❌ 压缩视频加载失败: ${error.message}`);
                log('💡 提示: 请先运行视频压缩脚本生成压缩版本');
                document.getElementById('videoStatus').textContent = '加载失败';
                document.getElementById('videoStatus').className = 'status error';
            }
        }

        // 测试渐进式加载
        async function testProgressiveLoading() {
            log('🎬 开始测试渐进式加载...');
            
            if (!window.EnhancedVideoManager) {
                log('❌ EnhancedVideoManager 未加载');
                return;
            }
            
            try {
                const manager = new EnhancedVideoManager();
                const video = await manager.loadVideoForPage('INDEX');
                
                log('✅ 渐进式加载测试完成');
                log(`📊 网络类型: ${manager.networkType.type}`);
                log(`📊 设备等级: ${manager.deviceCapability.level}`);
                log(`📊 选择质量: ${manager.qualitySettings.primary}`);
                
            } catch (error) {
                log(`❌ 渐进式加载测试失败: ${error.message}`);
            }
        }

        // 加载视频的Promise包装
        function loadVideo(videoElement, url) {
            return new Promise((resolve, reject) => {
                const timeout = setTimeout(() => {
                    reject(new Error('加载超时'));
                }, 30000);

                videoElement.addEventListener('canplaythrough', () => {
                    clearTimeout(timeout);
                    resolve();
                }, { once: true });

                videoElement.addEventListener('error', (error) => {
                    clearTimeout(timeout);
                    reject(error);
                }, { once: true });

                videoElement.src = url;
                videoElement.load();
            });
        }

        // 日志函数
        function log(message) {
            const logElement = document.getElementById('testLog');
            const timestamp = new Date().toLocaleTimeString();
            logElement.innerHTML += `[${timestamp}] ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
        }

        // 清空日志
        function clearLog() {
            document.getElementById('testLog').innerHTML = '';
        }
    </script>
</body>
</html>

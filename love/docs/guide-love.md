# Love Website 项目结构说明

## 📁 项目目录结构

```
love/
├── 📄 README.md                    # 项目说明文档
├── 📄 PROJECT-STRUCTURE.md         # 项目结构说明 (本文件)
├── 📄 package.json                 # Node.js 依赖配置
├── 📄 server.js                    # 主服务器文件
├── 📄 config.js                    # 全局配置文件
├── 📄 enhanced-video-manager.js    # 增强视频管理器
├── 📄 script.js                    # 主要前端脚本
├── 📄 style.css                    # 主要样式文件
├── 📄 pages.css                    # 页面样式文件
├── 📄 dynamic-styles.js            # 动态样式脚本
├── 📄 romantic-quotes.js           # 浪漫语录脚本
├── 📄 modern-quotes-data.js        # 现代语录数据
├── 📄 performance-report.js        # 性能报告脚本
│
├── 📁 html/                        # HTML页面文件
│   ├── 📄 index.html               # 主页
│   ├── 📄 anniversary.html         # 纪念日页面
│   ├── 📄 meetings.html            # 相遇页面
│   ├── 📄 memorial.html            # 纪念页面
│   ├── 📄 together-days.html       # 在一起的日子页面
│   └── 📄 modern-quotes-data.js    # 现代语录数据
│
├── 📁 test/                        # 测试文件目录
│   ├── 📄 test-api.html            # API测试页面
│   ├── 📄 test-star-quotes.html    # 星空语录测试
│   ├── 📄 test-together-days-api.html # 在一起天数API测试
│   ├── 📄 test-video-optimization.html # 视频优化测试页面 ⭐
│   ├── 📄 test-suite.html          # 测试套件
│   └── 📄 video-fix-test.html      # 视频修复测试
│
├── 📁 scripts/                     # 脚本文件目录
│   ├── 📄 video-optimizer-2k.sh    # 2K视频优化脚本 ⭐
│   ├── 📄 deploy-video-optimization.sh # 视频优化部署脚本 ⭐
│   ├── 📄 import-modern-quotes.js  # 导入现代语录脚本
│   ├── 📄 parse-poetry.js          # 诗词解析脚本
│   ├── 📄 video-manager.js         # 原始视频管理器
│   ├── 📄 video-init.js            # 视频初始化脚本
│   ├── 📄 video-performance-optimizer.js # 视频性能优化器
│   └── 📄 video-test.js            # 视频测试脚本
│
├── 📁 docs/                        # 文档目录
│   ├── 📄 视频加载优化完整方案.md    # 视频优化完整方案 ⭐
│   ├── 📄 总架构方案.md             # 总体架构方案
│   ├── 📄 guide-love.md            # 使用指南
│   ├── 📄 video-optimization-summary.md # 视频优化总结
│   └── 📄 test-execution-report.md # 测试执行报告
│
├── 📁 config/                      # 配置文件目录
│   ├── 📄 CONFIG-README.md         # 配置说明
│   ├── 📄 README.md                # 配置目录说明
│   ├── 📄 server-config.js         # 服务器配置
│   ├── 📄 update-config.js         # 配置更新脚本
│   ├── 📄 config-manager.sh        # 配置管理脚本
│   ├── 📄 manage-config.sh         # 配置管理脚本
│   └── 📄 config-usage-examples.md # 配置使用示例
│
├── 📁 background/                  # 视频背景文件目录
│   ├── 📄 flower-bg.mp4            # 花朵背景视频
│   ├── 📁 optimized/               # 优化后的视频文件 ⭐
│   │   ├── 📁 洱海/
│   │   │   └── 📄 DJI_0075_h265_optimized.mp4
│   │   └── 📄 (其他优化视频文件)
│   ├── 📁 花/                      # 花朵主题视频
│   ├── 📁 星河/                    # 星河主题视频
│   ├── 📁 洱海/                    # 洱海主题视频
│   ├── 📁 海底/                    # 海底主题视频
│   ├── 📁 绿荫/                    # 绿荫主题视频
│   └── 📁 sea/                     # 海洋主题视频
│
├── 📁 fonts/                       # 字体文件目录
│   ├── 📄 Courgette-Regular.ttf    # 英文字体
│   ├── 📄 GreatVibes-Regular.ttf   # 英文字体
│   ├── 📄 字小魂三分行楷(商用需授权).ttf
│   ├── 📄 字小魂勾玉行书(商用需授权).ttf
│   └── 📄 字魂行云飞白体(商用需授权).ttf
│
├── 📁 data/                        # 数据文件目录
│   ├── 📄 love_messages.db         # 爱情留言数据库
│   ├── 📄 现代美好情话千句搜索_.txt  # 现代情话数据
│   └── 📁 backups/                 # 数据备份目录
│
├── 📁 logs/                        # 日志文件目录
│   ├── 📄 backend.log              # 后端日志
│   └── 📄 manage.log               # 管理日志
│
├── 📁 temp/                        # 临时文件目录
│   ├── 📁 temp_compression/        # 压缩临时文件
│   └── 📁 temp_optimization/       # 优化临时文件
│
└── 📁 node_modules/                # Node.js 依赖包
```

## 🎯 重要文件说明

### ⭐ 视频优化相关 (新增)
- **`test/test-video-optimization.html`** - 视频性能测试页面
- **`scripts/video-optimizer-2k.sh`** - 保持2K质量的视频优化脚本
- **`docs/视频加载优化完整方案.md`** - 完整的优化方案文档
- **`enhanced-video-manager.js`** - 智能视频管理器

### 🌐 访问地址
- **主网站**: https://love.yuh.cool/
- **视频测试页面**: https://love.yuh.cool/test/test-video-optimization.html

### 📋 核心配置文件
- **`config.js`** - 全局配置，包含视频路径、API端点等
- **`server.js`** - Express服务器主文件
- **`package.json`** - 项目依赖和脚本配置

### 🎬 视频管理
- **原始视频**: `background/` 目录下的各主题视频
- **优化视频**: `background/optimized/` 目录下的压缩优化版本
- **视频管理器**: `enhanced-video-manager.js` 智能加载管理

## 🚀 快速开始

### 1. 运行视频优化
```bash
cd love
chmod +x scripts/video-optimizer-2k.sh
./scripts/video-optimizer-2k.sh
```

### 2. 测试优化效果
访问: https://love.yuh.cool/test/test-video-optimization.html

### 3. 启动服务器
```bash
npm start
# 或
node server.js
```

## 📝 开发说明

### 添加新的测试页面
将测试文件放在 `test/` 目录下，并使用相对路径引用资源：
```html
<script src="../config.js"></script>
<script src="../enhanced-video-manager.js"></script>
```

### 添加新的视频
1. 将原始视频放在 `background/` 相应主题目录下
2. 运行优化脚本生成压缩版本
3. 在 `config.js` 中添加配置

### 文档更新
所有文档统一放在 `docs/` 目录下，便于管理和查找。

---

**📞 技术支持**: 如有问题请查看 `docs/` 目录下的相关文档

#!/bin/bash

# Video Compression Script for Love Website
# 自动压缩视频文件，生成多种质量版本

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置
SOURCE_DIR="./background"
OUTPUT_DIR="./background/compressed"
TEMP_DIR="./temp_compression"

# 创建输出目录
mkdir -p "$OUTPUT_DIR"
mkdir -p "$TEMP_DIR"

echo -e "${BLUE}🎬 Love Website Video Compressor${NC}"
echo -e "${BLUE}=================================${NC}"

# 检查ffmpeg是否安装
if ! command -v ffmpeg &> /dev/null; then
    echo -e "${RED}❌ FFmpeg未安装，请先安装FFmpeg${NC}"
    echo "Ubuntu/Debian: sudo apt install ffmpeg"
    echo "macOS: brew install ffmpeg"
    exit 1
fi

# 压缩函数
compress_video() {
    local input_file="$1"
    local output_dir="$2"
    local filename=$(basename "$input_file")
    local name="${filename%.*}"
    local relative_path=$(dirname "${input_file#$SOURCE_DIR/}")
    
    # 创建相对路径的输出目录
    local target_dir="$output_dir/$relative_path"
    mkdir -p "$target_dir"
    
    echo -e "${YELLOW}📹 处理: $filename${NC}"
    
    # 获取原始文件信息
    local original_size=$(du -h "$input_file" | cut -f1)
    echo -e "   原始大小: $original_size"
    
    # 高质量版本 (1080p, 适合快速网络)
    echo -e "   🔄 生成高质量版本..."
    ffmpeg -i "$input_file" \
        -vf "scale=1920:1080:force_original_aspect_ratio=decrease,pad=1920:1080:(ow-iw)/2:(oh-ih)/2" \
        -c:v libx264 -preset medium -crf 23 \
        -c:a aac -b:a 128k \
        -movflags +faststart \
        -y "$target_dir/${name}_1080p.mp4" 2>/dev/null
    
    # 中等质量版本 (720p, 适合一般网络)
    echo -e "   🔄 生成中等质量版本..."
    ffmpeg -i "$input_file" \
        -vf "scale=1280:720:force_original_aspect_ratio=decrease,pad=1280:720:(ow-iw)/2:(oh-ih)/2" \
        -c:v libx264 -preset medium -crf 26 \
        -c:a aac -b:a 96k \
        -movflags +faststart \
        -y "$target_dir/${name}_720p.mp4" 2>/dev/null
    
    # 低质量版本 (480p, 适合慢速网络)
    echo -e "   🔄 生成低质量版本..."
    ffmpeg -i "$input_file" \
        -vf "scale=854:480:force_original_aspect_ratio=decrease,pad=854:480:(ow-iw)/2:(oh-ih)/2" \
        -c:v libx264 -preset medium -crf 28 \
        -c:a aac -b:a 64k \
        -movflags +faststart \
        -y "$target_dir/${name}_480p.mp4" 2>/dev/null
    
    # WebM版本 (更好的压缩率)
    echo -e "   🔄 生成WebM版本..."
    ffmpeg -i "$input_file" \
        -vf "scale=1280:720:force_original_aspect_ratio=decrease,pad=1280:720:(ow-iw)/2:(oh-ih)/2" \
        -c:v libvpx-vp9 -crf 30 -b:v 0 \
        -c:a libopus -b:a 96k \
        -y "$target_dir/${name}_720p.webm" 2>/dev/null
    
    # 显示压缩结果
    echo -e "${GREEN}   ✅ 压缩完成:${NC}"
    for quality in "1080p" "720p" "480p"; do
        local compressed_file="$target_dir/${name}_${quality}.mp4"
        if [ -f "$compressed_file" ]; then
            local compressed_size=$(du -h "$compressed_file" | cut -f1)
            echo -e "      ${quality}: $compressed_size"
        fi
    done
    
    local webm_file="$target_dir/${name}_720p.webm"
    if [ -f "$webm_file" ]; then
        local webm_size=$(du -h "$webm_file" | cut -f1)
        echo -e "      WebM: $webm_size"
    fi
    
    echo ""
}

# 查找并压缩所有视频文件
echo -e "${BLUE}🔍 搜索视频文件...${NC}"
find "$SOURCE_DIR" -type f \( -iname "*.mp4" -o -iname "*.MP4" \) | while read -r video_file; do
    # 跳过已经压缩的文件
    if [[ "$video_file" == *"/compressed/"* ]]; then
        continue
    fi
    
    compress_video "$video_file" "$OUTPUT_DIR"
done

echo -e "${GREEN}🎉 所有视频压缩完成！${NC}"
echo -e "${BLUE}压缩后的文件保存在: $OUTPUT_DIR${NC}"

# 生成压缩报告
echo -e "${BLUE}📊 生成压缩报告...${NC}"
{
    echo "# Video Compression Report"
    echo "Generated on: $(date)"
    echo ""
    echo "## Original Files"
    find "$SOURCE_DIR" -type f \( -iname "*.mp4" -o -iname "*.MP4" \) | while read -r file; do
        if [[ "$file" != *"/compressed/"* ]]; then
            size=$(du -h "$file" | cut -f1)
            echo "- $(basename "$file"): $size"
        fi
    done
    echo ""
    echo "## Compressed Files"
    find "$OUTPUT_DIR" -type f \( -iname "*.mp4" -o -iname "*.webm" \) | while read -r file; do
        size=$(du -h "$file" | cut -f1)
        echo "- $(basename "$file"): $size"
    done
} > "$OUTPUT_DIR/compression_report.md"

echo -e "${GREEN}✅ 压缩报告已生成: $OUTPUT_DIR/compression_report.md${NC}"

# 清理临时文件
rm -rf "$TEMP_DIR"

echo -e "${BLUE}🚀 下一步：更新配置文件以使用压缩后的视频${NC}"
